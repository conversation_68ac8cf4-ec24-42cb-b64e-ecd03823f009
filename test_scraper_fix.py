#!/usr/bin/env python3
"""
Test the scraper with the timestamp fix
"""
import requests
import time

def test_scraper_endpoint():
    """Test that the scraper endpoint works without timestamp errors"""
    
    print("🧪 Testing Scraper Endpoint with Timestamp Fix")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Wait for server to start
    print("Waiting for server to start...")
    time.sleep(5)
    
    try:
        # Test 1: Check if server is running
        print("\n1. Testing server health...")
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            print("✅ Server is healthy")
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return False
        
        # Test 2: Try to trigger a scraper job
        print("\n2. Testing scraper job trigger...")
        try:
            response = requests.post(f"{base_url}/run-scraper", timeout=30)
            if response.status_code == 200:
                print("✅ Scraper job triggered successfully")
                
                # Extract job ID from response if possible
                html_content = response.text
                if 'job_id' in html_content.lower():
                    print("✅ Job ID found in response")
                    
                    # Wait a bit for the job to process
                    print("   Waiting for job to process...")
                    time.sleep(10)
                    
                else:
                    print("⚠️  No job ID found in response")
                    
            else:
                print(f"❌ Scraper job trigger failed: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Error triggering scraper job: {e}")
        
        # Test 3: Check database for recent records
        print("\n3. Testing database access...")
        try:
            response = requests.get(f"{base_url}/database", timeout=30)
            if response.status_code == 200:
                html_content = response.text
                
                # Count records in the view
                tr_count = html_content.count('<tr') - 1  # Subtract header row
                print(f"✅ Database view accessible with {tr_count} records")
                
                # Check for fallback indicators
                if 'fallback' in html_content.lower():
                    print("✅ Fallback indicators present in database view")
                else:
                    print("⚠️  No fallback indicators found in database view")
                    
            else:
                print(f"❌ Database view failed: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Error accessing database view: {e}")
        
        # Test 4: Check for any error logs in the server
        print("\n4. Checking for timestamp-related errors...")
        
        # We can't directly access server logs, but we can check if operations complete
        try:
            # Try a simple database operation through the API
            response = requests.get(f"{base_url}/database?start_date=2025-06-20&end_date=2025-06-23", timeout=30)
            if response.status_code == 200:
                print("✅ Date-filtered database query successful")
            else:
                print(f"❌ Date-filtered database query failed: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Error with date-filtered query: {e}")
        
        print("\n" + "=" * 50)
        print("🎯 SUMMARY:")
        print("If no timestamp binding errors appear above, the fix is working!")
        print("The scraper should now handle fallback data without database errors.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False

if __name__ == '__main__':
    success = test_scraper_endpoint()
    if success:
        print("\n✅ Scraper endpoint test completed!")
        print("Check the server logs for any timestamp binding errors.")
        print("If none appear, the timestamp fix is working correctly.")
    else:
        print("\n❌ Scraper endpoint test failed!")
        exit(1)
