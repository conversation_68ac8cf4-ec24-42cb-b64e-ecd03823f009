#!/usr/bin/env python3
"""
Test the timestamp binding fix for SQLite database operations
"""
import sys
sys.path.append('.')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from app.models.gas_price import GasPrice
from app.db.database import save_gas_prices, convert_timestamp
import logging

# Set up logging to see debug messages
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_timestamp_conversion():
    """Test the convert_timestamp function with various timestamp types"""
    
    print("🧪 Testing Timestamp Conversion Function")
    print("=" * 50)
    
    # Test cases with different timestamp types
    test_cases = [
        ("datetime object", datetime.now()),
        ("string ISO format", "2025-06-23T10:54:20.782"),
        ("string date only", "2025-06-23"),
        ("None value", None),
        ("pandas Timestamp", pd.Timestamp.now()),
        ("numpy datetime64", np.datetime64('2025-06-23T10:54:20')),
        ("Unix timestamp (int)", 1719140060),
        ("Unix timestamp (float)", 1719140060.782),
    ]
    
    for test_name, test_value in test_cases:
        try:
            print(f"\n🔍 Testing {test_name}: {test_value} (type: {type(test_value)})")
            result = convert_timestamp(test_value)
            print(f"   ✅ Result: {result} (type: {type(result)})")
            
            # Verify the result is SQLite-compatible
            if result is not None and not isinstance(result, str):
                print(f"   ❌ ERROR: Result should be string or None, got {type(result)}")
            else:
                print(f"   ✅ SQLite-compatible type")
                
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
    
    return True

def test_database_operations_with_various_timestamps():
    """Test database operations with various timestamp types that might cause binding errors"""
    
    print("\n🧪 Testing Database Operations with Various Timestamps")
    print("=" * 60)
    
    try:
        # Create test data with different timestamp scenarios
        current_time = datetime.now()
        past_time = current_time - timedelta(days=1)
        
        # Test case 1: Regular datetime objects
        print("\n1. Testing with regular datetime objects...")
        regular_price = GasPrice(
            country="TimestampFixTest",
            company="RegularDateTime",
            url="https://test.com/regular",
            element="regular_element",
            product_type="Butane",
            product_subtype="Regular",
            name="Regular DateTime Test",
            weight=12.5,
            price=15.99,
            image_path="regular.jpg",
            date_recorded=current_time,
            uid="regular_123",
            is_fallback_data=False,
            original_scrape_date=past_time
        )
        
        save_gas_prices([regular_price])
        print("✅ Regular datetime objects saved successfully")
        
        # Test case 2: Pandas Timestamp objects
        print("\n2. Testing with pandas Timestamp objects...")
        pandas_price = GasPrice(
            country="TimestampFixTest",
            company="PandasTimestamp",
            url="https://test.com/pandas",
            element="pandas_element",
            product_type="Propane",
            product_subtype="Regular",
            name="Pandas Timestamp Test",
            weight=12.5,
            price=16.99,
            image_path="pandas.jpg",
            date_recorded=pd.Timestamp.now(),
            uid="pandas_123",
            is_fallback_data=False,
            original_scrape_date=pd.Timestamp(past_time)
        )
        
        save_gas_prices([pandas_price])
        print("✅ Pandas Timestamp objects saved successfully")
        
        # Test case 3: Mixed timestamp types (simulating real scraper data)
        print("\n3. Testing with mixed timestamp types...")
        
        # Create a DataFrame like the scraper would
        df_data = {
            'country': ['TimestampFixTest'],
            'company': ['MixedTimestamps'],
            'url': ['https://test.com/mixed'],
            'element_nr': ['mixed_element'],
            'product': ['Butane'],
            'product_type': ['Regular'],
            'comercial_name': ['Mixed Timestamps Test'],
            'weight': [12.5],
            'price': [17.99],
            'photo': ['mixed.jpg'],
            'uid': ['mixed_123'],
            'monthly_median_income': [1000.0],
            'yearly_median_income': [12000.0],
            'is_fallback_data': [True],
            'original_scrape_date': [pd.Timestamp(past_time)]  # This might cause the binding error
        }
        
        df = pd.DataFrame(df_data)
        
        # Convert DataFrame row to GasPrice (simulating scraper behavior)
        row = df.iloc[0]
        mixed_price = GasPrice(
            country=row['country'],
            company=row['company'],
            url=row['url'],
            element=row['element_nr'],
            product_type=row['product'],
            product_subtype=row['product_type'],
            name=row['comercial_name'],
            weight=row['weight'],
            price=row['price'],
            image_path=row['photo'],
            date_recorded=pd.Timestamp.now(),  # Pandas timestamp
            uid=row['uid'],
            monthly_median_income=row['monthly_median_income'],
            yearly_median_income=row['yearly_median_income'],
            is_fallback_data=row['is_fallback_data'],
            original_scrape_date=row['original_scrape_date']  # Pandas timestamp from DataFrame
        )
        
        save_gas_prices([mixed_price])
        print("✅ Mixed timestamp types saved successfully")
        
        # Test case 4: Numpy datetime64 objects
        print("\n4. Testing with numpy datetime64 objects...")
        numpy_price = GasPrice(
            country="TimestampFixTest",
            company="NumpyDatetime64",
            url="https://test.com/numpy",
            element="numpy_element",
            product_type="Propane",
            product_subtype="Regular",
            name="Numpy Datetime64 Test",
            weight=12.5,
            price=18.99,
            image_path="numpy.jpg",
            date_recorded=datetime.now(),
            uid="numpy_123",
            is_fallback_data=False,
            original_scrape_date=pd.to_datetime(np.datetime64(past_time))
        )
        
        save_gas_prices([numpy_price])
        print("✅ Numpy datetime64 objects saved successfully")
        
        # Test case 5: Edge case - None original_scrape_date
        print("\n5. Testing with None original_scrape_date...")
        none_price = GasPrice(
            country="TimestampFixTest",
            company="NoneTimestamp",
            url="https://test.com/none",
            element="none_element",
            product_type="Butane",
            product_subtype="Regular",
            name="None Timestamp Test",
            weight=12.5,
            price=19.99,
            image_path="none.jpg",
            date_recorded=current_time,
            uid="none_123",
            is_fallback_data=False,
            original_scrape_date=None
        )
        
        save_gas_prices([none_price])
        print("✅ None original_scrape_date handled successfully")
        
        print("\n" + "=" * 60)
        print("🎉 ALL DATABASE OPERATIONS SUCCESSFUL!")
        print("The timestamp binding error has been resolved.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Database operation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_mechanism_with_fix():
    """Test that the fallback mechanism works with the timestamp fix"""
    
    print("\n🧪 Testing Fallback Mechanism with Timestamp Fix")
    print("=" * 55)
    
    try:
        from app.services.fallback_service import fallback_service
        
        # Reset statistics
        fallback_service.reset_statistics()
        
        # Create original data with pandas timestamp
        original_time = pd.Timestamp.now() - pd.Timedelta(days=1)
        original_price = GasPrice(
            country="FallbackFixTest",
            company="FallbackCompany",
            url="https://fallback.com/original",
            element="fallback_element",
            product_type="Butane",
            product_subtype="Regular",
            name="Fallback Test Butane",
            weight=12.5,
            price=20.99,
            image_path="fallback.jpg",
            date_recorded=original_time,
            uid="fallback_original_123",
            is_fallback_data=False,
            original_scrape_date=original_time
        )
        
        # Save original data
        save_gas_prices([original_price])
        print("✅ Original fallback data saved")
        
        # Attempt fallback
        fallback_price = fallback_service.attempt_fallback(
            country="FallbackFixTest",
            company="FallbackCompany",
            url="https://fallback.com/failed",
            element="fallback_element",
            product_type="Butane",
            product_subtype="Regular",
            name="Fallback Test Butane",
            weight=12.5,
            uid="fallback_test_456",
            current_scrape_date=pd.Timestamp.now()  # Use pandas timestamp
        )
        
        if fallback_price:
            print("✅ Fallback service created fallback data")
            
            # Save fallback data
            save_gas_prices([fallback_price])
            print("✅ Fallback data saved successfully")
            
            # Check statistics
            stats = fallback_service.get_fallback_statistics()
            print(f"✅ Fallback statistics: {stats['successful_fallbacks']} successful")
            
        else:
            print("❌ Fallback service failed to create fallback data")
            return False
        
        print("✅ Fallback mechanism working with timestamp fix")
        return True
        
    except Exception as e:
        print(f"❌ Fallback mechanism test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 Starting Timestamp Binding Fix Tests")
    print("=" * 60)
    
    # Test 1: Timestamp conversion function
    test1_success = test_timestamp_conversion()
    
    # Test 2: Database operations with various timestamp types
    test2_success = test_database_operations_with_various_timestamps()
    
    # Test 3: Fallback mechanism with timestamp fix
    test3_success = test_fallback_mechanism_with_fix()
    
    if test1_success and test2_success and test3_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("The SQLite timestamp binding error has been completely resolved.")
        print("The scraper should now work without database errors.")
    else:
        print("\n❌ Some tests failed. The timestamp binding issue may persist.")
        exit(1)
