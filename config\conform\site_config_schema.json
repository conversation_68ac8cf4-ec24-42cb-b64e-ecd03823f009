{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Gas Bottle Site Configuration Schema", "description": "Schema for validating gas bottle scraping site configurations", "type": "object", "patternProperties": {"^[A-Za-z]+$": {"type": "object", "description": "Country configuration", "patternProperties": {"^[A-Za-z0-9_]+$": {"type": "object", "description": "Company configuration", "patternProperties": {"^Website(_\\d+)?$": {"type": "object", "description": "Website configuration", "required": ["url"], "properties": {"url": {"type": "string", "format": "uri", "description": "Website URL to scrape"}, "accept_cookies": {"type": "boolean", "description": "Whether to accept cookies"}, "kill_cookies": {"type": "boolean", "description": "Whether to remove cookie banners"}, "cookie_modal": {"type": "string", "description": "CSS selector for cookie modal"}, "zip_code": {"type": "boolean", "description": "Whether zip code entry is required"}, "zip_type": {"type": "string", "enum": ["separated", "joined"], "description": "Type of zip code entry"}, "zip_behaviour": {"type": "string", "enum": ["click", "wait"], "description": "Behavior after zip code entry"}, "zip_submit_selector": {"type": "string", "description": "CSS selector for zip code submit button"}, "cp4_selector": {"type": "string", "description": "CSS selector for first part of zip code"}, "cp3_selector": {"type": "string", "description": "CSS selector for second part of zip code"}, "cp4_text": {"type": "string", "description": "First part of zip code value"}, "cp3_text": {"type": "string", "description": "Second part of zip code value"}, "zip_code_selector": {"type": "string", "description": "CSS selector for joined zip code input"}, "zip_code_text": {"type": "string", "description": "Complete zip code value"}, "dropdown_location": {"type": "boolean", "description": "Whether dropdown location selection is required"}, "concelho_selector": {"type": "string", "description": "CSS selector for district dropdown"}, "concelho_value": {"type": "string", "description": "CSS selector for district option"}, "freguesia_selector": {"type": "string", "description": "CSS selector for parish dropdown"}, "freguesia_value": {"type": "string", "description": "CSS selector for parish option"}, "set_bottles": {"type": "boolean", "description": "Whether bottle quantity setting is required"}, "set_bootles_selector": {"type": "string", "description": "CSS selector for bottle quantity controls"}, "Elements": {"type": "object", "description": "Product elements to scrape", "patternProperties": {"^\\d+_element$": {"type": "object", "required": ["name", "price", "product"], "properties": {"name": {"type": "string", "description": "CSS selector for product name"}, "weight": {"type": "string", "description": "CSS selector for product weight"}, "weight_text": {"type": "string", "description": "Static weight value if selector not available"}, "price": {"type": "string", "description": "CSS selector for product price"}, "product": {"type": "string", "enum": ["Butane", "Propane"], "description": "Product type"}, "product_type": {"type": "string", "enum": ["Light", "Regular"], "description": "Product subtype"}, "photo": {"type": "string", "description": "Path to product image"}, "uid": {"type": "string", "pattern": "^[a-z]+_[a-z0-9_]+_\\d+$", "description": "Unique identifier for the product"}}}}}}}}}}}}}