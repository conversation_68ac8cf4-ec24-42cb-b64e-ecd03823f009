#!/usr/bin/env python3
"""
Test the database timestamp fix
"""
import sys
sys.path.append('.')

from datetime import datetime, timedelta
from app.models.gas_price import GasPrice
from app.db.database import save_gas_prices, get_gas_prices
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_timestamp_fix():
    """Test that the database can handle timestamps correctly"""
    
    print("🧪 Testing Database Timestamp Fix")
    print("=" * 40)
    
    try:
        # Create test data with various timestamp scenarios
        current_time = datetime.now()
        past_time = current_time - timedelta(days=2)
        
        # Test 1: Fresh data (not fallback)
        fresh_price = GasPrice(
            country="TimestampTestCountry",
            company="TimestampTestCompany",
            url="https://timestamptest.com",
            element="timestamp_element_1",
            product_type="Butane",
            product_subtype="Regular",
            name="Timestamp Test Butane 12.5kg",
            weight=12.5,
            price=17.99,
            image_path="timestamptest.jpg",
            date_recorded=current_time,
            uid="timestamp_fresh_123",
            monthly_median_income=1000.0,
            yearly_median_income=12000.0,
            price_per_kg=1.44,
            price_as_percent_of_monthly_income=1.8,
            type="T3",
            is_fallback_data=False,
            original_scrape_date=current_time
        )
        
        # Test 2: Fallback data
        fallback_price = GasPrice(
            country="TimestampTestCountry",
            company="TimestampTestCompany",
            url="https://timestamptest.com/failed",
            element="timestamp_element_1",
            product_type="Butane",
            product_subtype="Regular",
            name="Timestamp Test Butane 12.5kg",
            weight=12.5,
            price=17.99,  # Same price as original
            image_path="timestamptest.jpg",
            date_recorded=current_time,  # Current scrape time
            uid="timestamp_fallback_456",
            monthly_median_income=1000.0,
            yearly_median_income=12000.0,
            price_per_kg=1.44,
            price_as_percent_of_monthly_income=1.8,
            type="T3",
            is_fallback_data=True,  # This is fallback data
            original_scrape_date=past_time  # Original scrape time
        )
        
        print("1. Testing database save operation...")
        
        # Save the test data
        save_gas_prices([fresh_price, fallback_price])
        print("✅ Successfully saved test data to database")
        
        print("\n2. Testing database retrieval operation...")
        
        # Retrieve the data
        retrieved_data = get_gas_prices(
            countries=["TimestampTestCountry"],
            companies=["TimestampTestCompany"]
        )
        
        if len(retrieved_data) >= 2:
            print(f"✅ Successfully retrieved {len(retrieved_data)} records")
            
            # Check data integrity
            fresh_record = next((p for p in retrieved_data if not p.is_fallback_data), None)
            fallback_record = next((p for p in retrieved_data if p.is_fallback_data), None)
            
            if fresh_record and fallback_record:
                print("✅ Found both fresh and fallback records")
                
                # Check timestamp types
                print(f"✅ Fresh record date_recorded type: {type(fresh_record.date_recorded)}")
                print(f"✅ Fresh record original_scrape_date type: {type(fresh_record.original_scrape_date)}")
                print(f"✅ Fallback record date_recorded type: {type(fallback_record.date_recorded)}")
                print(f"✅ Fallback record original_scrape_date type: {type(fallback_record.original_scrape_date)}")
                
                # Check timestamp relationships
                if fallback_record.original_scrape_date < fallback_record.date_recorded:
                    print("✅ Fallback record has correct timestamp relationship")
                else:
                    print("❌ Fallback record timestamp relationship incorrect")
                
                # Check data values
                if fresh_record.price == fallback_record.price:
                    print("✅ Price data preserved correctly")
                else:
                    print("❌ Price data not preserved correctly")
                    
                if fresh_record.is_fallback_data == False and fallback_record.is_fallback_data == True:
                    print("✅ Fallback flags set correctly")
                else:
                    print("❌ Fallback flags not set correctly")
                    
            else:
                print("❌ Missing fresh or fallback records")
                
        else:
            print(f"❌ Expected at least 2 records, got {len(retrieved_data)}")
            
        print("\n3. Testing timestamp conversion edge cases...")
        
        # Test with None values
        test_price_none = GasPrice(
            country="NoneTestCountry",
            company="NoneTestCompany",
            url="https://nonetest.com",
            element="none_element_1",
            product_type="Propane",
            product_subtype="Regular",
            name="None Test Propane 12.5kg",
            weight=12.5,
            price=19.99,
            image_path="nonetest.jpg",
            date_recorded=current_time,
            uid="none_test_123",
            is_fallback_data=False,
            original_scrape_date=None  # Test None handling
        )
        
        save_gas_prices([test_price_none])
        print("✅ Successfully handled None original_scrape_date")
        
        print("\n" + "=" * 40)
        print("🎉 Database timestamp fix test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Database timestamp fix test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_database_timestamp_fix()
    if success:
        print("\n✅ Database timestamp fix is working correctly!")
    else:
        print("\n❌ Database timestamp fix needs more work.")
        exit(1)
