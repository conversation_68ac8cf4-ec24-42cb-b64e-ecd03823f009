#!/usr/bin/env python3
"""
Complete verification that the timestamp binding fix is working
"""
import sys
sys.path.append('.')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from app.models.gas_price import GasPrice
from app.db.database import save_gas_prices, get_gas_prices
from app.services.fallback_service import fallback_service
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_all_timestamp_scenarios():
    """Test all possible timestamp scenarios that could cause binding errors"""
    
    print("🔍 COMPREHENSIVE TIMESTAMP BINDING FIX VERIFICATION")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: Pandas Timestamp (most likely culprit)
    print("\n1. Testing Pandas Timestamp objects...")
    try:
        pandas_price = GasPrice(
            country="PandasTest",
            company="PandasCompany",
            url="https://pandas.test",
            element="pandas_1",
            product_type="Butane",
            product_subtype="Regular",
            name="Pandas Test Product",
            weight=12.5,
            price=15.99,
            image_path="pandas.jpg",
            date_recorded=pd.Timestamp.now(),
            uid="pandas_123",
            is_fallback_data=False,
            original_scrape_date=pd.Timestamp.now() - pd.Timedelta(days=1)
        )
        
        save_gas_prices([pandas_price])
        print("✅ Pandas Timestamp objects - SUCCESS")
        test_results.append(("Pandas Timestamp", True))
        
    except Exception as e:
        print(f"❌ Pandas Timestamp objects - FAILED: {e}")
        test_results.append(("Pandas Timestamp", False))
    
    # Test 2: DataFrame-generated timestamps (scraper scenario)
    print("\n2. Testing DataFrame-generated timestamps...")
    try:
        df_data = {
            'country': ['DataFrameTest'],
            'company': ['DataFrameCompany'],
            'url': ['https://dataframe.test'],
            'element_nr': ['df_1'],
            'product': ['Propane'],
            'product_type': ['Regular'],
            'comercial_name': ['DataFrame Test Product'],
            'weight': [12.5],
            'price': [16.99],
            'photo': ['dataframe.jpg'],
            'uid': ['df_123'],
            'monthly_median_income': [1000.0],
            'yearly_median_income': [12000.0],
            'is_fallback_data': [True],
            'original_scrape_date': [pd.Timestamp.now() - pd.Timedelta(days=2)]
        }
        
        df = pd.DataFrame(df_data)
        
        # Simulate scraper processing
        for _, row in df.iterrows():
            df_price = GasPrice(
                country=row['country'],
                company=row['company'],
                url=row['url'],
                element=row['element_nr'],
                product_type=row['product'],
                product_subtype=row['product_type'],
                name=row['comercial_name'],
                weight=row['weight'],
                price=row['price'],
                image_path=row['photo'],
                date_recorded=pd.Timestamp.now(),  # This was causing the error
                uid=row['uid'],
                monthly_median_income=row['monthly_median_income'],
                yearly_median_income=row['yearly_median_income'],
                is_fallback_data=row['is_fallback_data'],
                original_scrape_date=row['original_scrape_date']  # This was parameter 19
            )
            
            save_gas_prices([df_price])
        
        print("✅ DataFrame-generated timestamps - SUCCESS")
        test_results.append(("DataFrame timestamps", True))
        
    except Exception as e:
        print(f"❌ DataFrame-generated timestamps - FAILED: {e}")
        test_results.append(("DataFrame timestamps", False))
    
    # Test 3: Fallback mechanism with various timestamp types
    print("\n3. Testing fallback mechanism with timestamp fix...")
    try:
        fallback_service.reset_statistics()
        
        # Create original data
        original_price = GasPrice(
            country="FallbackTest",
            company="FallbackCompany",
            url="https://fallback.test",
            element="fallback_1",
            product_type="Butane",
            product_subtype="Regular",
            name="Fallback Test Product",
            weight=12.5,
            price=17.99,
            image_path="fallback.jpg",
            date_recorded=pd.Timestamp.now() - pd.Timedelta(days=1),
            uid="fallback_original_123",
            is_fallback_data=False,
            original_scrape_date=pd.Timestamp.now() - pd.Timedelta(days=1)
        )
        
        save_gas_prices([original_price])
        
        # Attempt fallback
        fallback_price = fallback_service.attempt_fallback(
            country="FallbackTest",
            company="FallbackCompany",
            url="https://fallback.test/failed",
            element="fallback_1",
            product_type="Butane",
            product_subtype="Regular",
            name="Fallback Test Product",
            weight=12.5,
            uid="fallback_test_456",
            current_scrape_date=pd.Timestamp.now()
        )
        
        if fallback_price:
            save_gas_prices([fallback_price])
            print("✅ Fallback mechanism with timestamps - SUCCESS")
            test_results.append(("Fallback mechanism", True))
        else:
            print("❌ Fallback mechanism - FAILED: No fallback data created")
            test_results.append(("Fallback mechanism", False))
        
    except Exception as e:
        print(f"❌ Fallback mechanism - FAILED: {e}")
        test_results.append(("Fallback mechanism", False))
    
    # Test 4: Mixed timestamp types in batch
    print("\n4. Testing mixed timestamp types in batch...")
    try:
        mixed_prices = [
            GasPrice(
                country="MixedTest",
                company="RegularDateTime",
                url="https://mixed.test/1",
                element="mixed_1",
                product_type="Butane",
                weight=12.5,
                price=18.99,
                date_recorded=datetime.now(),  # Regular datetime
                uid="mixed_1",
                is_fallback_data=False,
                original_scrape_date=datetime.now()
            ),
            GasPrice(
                country="MixedTest",
                company="PandasTimestamp",
                url="https://mixed.test/2",
                element="mixed_2",
                product_type="Propane",
                weight=12.5,
                price=19.99,
                date_recorded=pd.Timestamp.now(),  # Pandas timestamp
                uid="mixed_2",
                is_fallback_data=True,
                original_scrape_date=pd.Timestamp.now() - pd.Timedelta(days=1)
            ),
            GasPrice(
                country="MixedTest",
                company="NumpyDatetime",
                url="https://mixed.test/3",
                element="mixed_3",
                product_type="Butane",
                weight=12.5,
                price=20.99,
                date_recorded=datetime.now(),
                uid="mixed_3",
                is_fallback_data=False,
                original_scrape_date=pd.to_datetime(np.datetime64('2025-06-22'))  # Numpy converted
            )
        ]
        
        save_gas_prices(mixed_prices)
        print("✅ Mixed timestamp types in batch - SUCCESS")
        test_results.append(("Mixed batch timestamps", True))
        
    except Exception as e:
        print(f"❌ Mixed timestamp types in batch - FAILED: {e}")
        test_results.append(("Mixed batch timestamps", False))
    
    # Test 5: Data retrieval and integrity
    print("\n5. Testing data retrieval and integrity...")
    try:
        # Retrieve all test data
        all_data = get_gas_prices()
        test_data = [p for p in all_data if 'Test' in p.country]
        
        print(f"✅ Retrieved {len(test_data)} test records")
        
        # Check timestamp types
        for price in test_data[:3]:  # Check first 3
            if not isinstance(price.date_recorded, datetime):
                raise ValueError(f"date_recorded should be datetime, got {type(price.date_recorded)}")
            if price.original_scrape_date and not isinstance(price.original_scrape_date, datetime):
                raise ValueError(f"original_scrape_date should be datetime, got {type(price.original_scrape_date)}")
        
        print("✅ Data retrieval and integrity - SUCCESS")
        test_results.append(("Data retrieval", True))
        
    except Exception as e:
        print(f"❌ Data retrieval and integrity - FAILED: {e}")
        test_results.append(("Data retrieval", False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TIMESTAMP BINDING FIX VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed_tests = sum(1 for _, success in test_results if success)
    total_tests = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\n🎯 OVERALL RESULT: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("\n🎉 COMPLETE SUCCESS!")
        print("The SQLite timestamp binding error has been completely resolved.")
        print("\n🔧 TECHNICAL DETAILS:")
        print("• Parameter 19 (original_scrape_date) binding error fixed")
        print("• Pandas Timestamp objects properly converted to strings")
        print("• DataFrame-generated timestamps handled correctly")
        print("• Fallback mechanism works with all timestamp types")
        print("• Mixed timestamp batches processed successfully")
        print("• Data integrity maintained throughout")
        
        print("\n✅ THE SCRAPER CAN NOW RUN WITHOUT DATABASE ERRORS!")
        return True
    else:
        print(f"\n❌ {total_tests - passed_tests} tests failed.")
        print("Some timestamp binding issues may still exist.")
        return False

if __name__ == '__main__':
    success = test_all_timestamp_scenarios()
    if not success:
        exit(1)
