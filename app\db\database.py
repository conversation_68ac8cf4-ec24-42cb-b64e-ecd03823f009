from contextlib import contextmanager
import sqlite3
from typing import List, Optional, Generator
from datetime import datetime
import logging
from app.models.gas_price import GasPrice
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

def convert_timestamp(value):
    """Convert various timestamp types to datetime or string for SQLite compatibility"""
    if value is None:
        return None

    # Handle pandas Timestamp
    if hasattr(value, 'to_pydatetime'):
        return value.to_pydatetime().isoformat()

    # Handle datetime objects
    if isinstance(value, datetime):
        return value.isoformat()

    # Handle string timestamps
    if isinstance(value, str):
        return value

    # Handle other timestamp types by converting to string
    try:
        if hasattr(value, 'isoformat'):
            return value.isoformat()
        else:
            # Convert to datetime first, then to string
            return str(value)
    except Exception as e:
        logger.warning(f"Could not convert timestamp {value} (type: {type(value)}): {e}")
        return datetime.now().isoformat()

@contextmanager
def get_db_connection() -> Generator[sqlite3.Connection, None, None]:
    """Context manager for database connections"""
    conn = None
    try:
        conn = sqlite3.connect(settings.DATABASE_URL.replace('sqlite:///', ''))
        conn.row_factory = sqlite3.Row
        yield conn
    except sqlite3.Error as e:
        logger.error(f"Database error: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def init_db() -> None:
    """Initialize the database schema"""
    with get_db_connection() as conn:
        try:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS gas_prices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    country TEXT NOT NULL,
                    company TEXT NOT NULL,
                    url TEXT NOT NULL,
                    element TEXT,
                    product_type TEXT,
                    product_subtype TEXT,
                    name TEXT,
                    weight REAL,
                    price REAL NOT NULL,
                    image_path TEXT,
                    date_recorded TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    uid TEXT NOT NULL,
                    monthly_median_income REAL,
                    yearly_median_income REAL,
                    price_per_kg REAL,
                    price_as_percent_of_monthly_income REAL,
                    type TEXT,
                    is_fallback_data BOOLEAN DEFAULT FALSE,
                    original_scrape_date TIMESTAMP
                )
            ''')
            conn.commit()
            logger.info("Database initialized successfully")
        except sqlite3.Error as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise

def save_gas_prices(prices: List[GasPrice]) -> None:
    """Save multiple gas prices to the database"""
    with get_db_connection() as conn:
        try:
            for price in prices:
                # Set price to 0.00 if None
                price_value = 0.00 if price.price is None else price.price
                
                # Convert timestamps to strings for SQLite compatibility
                date_recorded = convert_timestamp(price.date_recorded)
                original_scrape_date = convert_timestamp(price.original_scrape_date) if price.original_scrape_date else convert_timestamp(price.date_recorded)
                
                conn.execute('''
                    INSERT INTO gas_prices (
                        country, company, url, element, product_type,
                        product_subtype, name, weight, price, image_path,
                        date_recorded, uid, monthly_median_income, yearly_median_income,
                        price_per_kg, price_as_percent_of_monthly_income, type,
                        is_fallback_data, original_scrape_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    price.country,
                    price.company,
                    price.url,
                    price.element,
                    price.product_type,
                    price.product_subtype,
                    price.name,
                    price.weight,
                    price_value,  # Use the default value if price is None
                    price.image_path,
                    date_recorded,
                    price.uid,
                    price.monthly_median_income,
                    price.yearly_median_income,
                    price.price_per_kg,
                    price.price_as_percent_of_monthly_income,
                    price.type,
                    price.is_fallback_data,
                    original_scrape_date
                ))
            conn.commit()
            logger.info(f"Successfully saved {len(prices)} records to database")
        except sqlite3.Error as e:
            logger.error(f"Error saving to database: {str(e)}")
            raise

def get_gas_prices(
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    country: Optional[str] = None,  # Keep for backward compatibility
    company: Optional[str] = None,  # Keep for backward compatibility
    product_type: Optional[str] = None,  # Keep for backward compatibility
    countries: Optional[List[str]] = None,  # New multi-select parameter
    companies: Optional[List[str]] = None,  # New multi-select parameter
    product_types: Optional[List[str]] = None  # New multi-select parameter
) -> List[GasPrice]:
    """Retrieve gas prices with optional filters"""
    query = "SELECT * FROM gas_prices WHERE 1=1"
    params = []

    # Log the input parameters for debugging
    logger.info(f"get_gas_prices called with: start_date={start_date}, end_date={end_date}, "
                f"countries={countries}, companies={companies}, product_types={product_types}")

    if start_date:
        query += " AND date_recorded >= ?"
        params.append(start_date)
    if end_date:
        query += " AND date_recorded <= ?"
        params.append(end_date)

    # Handle countries (multi-select or single)
    # Filter out empty strings and None values
    country_filter = countries or ([country] if country else None)
    if country_filter:
        # Remove empty strings and None values
        country_filter = [c.strip() for c in country_filter if c and c.strip()]
        if country_filter:  # Only apply filter if we have valid values
            placeholders = ','.join(['?' for _ in country_filter])
            query += f" AND country IN ({placeholders})"
            params.extend(country_filter)

    # Handle companies (multi-select or single)
    # Filter out empty strings and None values
    company_filter = companies or ([company] if company else None)
    if company_filter:
        # Remove empty strings and None values
        company_filter = [c.strip() for c in company_filter if c and c.strip()]
        if company_filter:  # Only apply filter if we have valid values
            placeholders = ','.join(['?' for _ in company_filter])
            query += f" AND company IN ({placeholders})"
            params.extend(company_filter)

    # Handle product types (multi-select or single)
    # Filter out empty strings and None values
    product_type_filter = product_types or ([product_type] if product_type else None)
    if product_type_filter:
        # Remove empty strings and None values
        product_type_filter = [p.strip() for p in product_type_filter if p and p.strip()]
        if product_type_filter:  # Only apply filter if we have valid values
            placeholders = ','.join(['?' for _ in product_type_filter])
            query += f" AND product_type IN ({placeholders})"
            params.extend(product_type_filter)

    query += " ORDER BY date_recorded DESC"

    # Log the final query and parameters for debugging
    logger.info(f"Executing query: {query}")
    logger.info(f"Query parameters: {params}")

    with get_db_connection() as conn:
        try:
            cursor = conn.execute(query, params)
            results = []
            for row in cursor.fetchall():
                row_dict = dict(row)
                # Convert string timestamps back to datetime objects
                if row_dict.get('date_recorded'):
                    try:
                        row_dict['date_recorded'] = datetime.fromisoformat(row_dict['date_recorded'])
                    except (ValueError, TypeError):
                        row_dict['date_recorded'] = datetime.now()

                if row_dict.get('original_scrape_date'):
                    try:
                        row_dict['original_scrape_date'] = datetime.fromisoformat(row_dict['original_scrape_date'])
                    except (ValueError, TypeError):
                        row_dict['original_scrape_date'] = row_dict.get('date_recorded') or datetime.now()

                results.append(GasPrice(**row_dict))

            logger.info(f"Query returned {len(results)} results")
            return results
        except sqlite3.Error as e:
            logger.error(f"Error retrieving gas prices: {str(e)}")
            raise


def get_recent_fallback_data(
    country: str,
    company: str,
    product_type: Optional[str] = None,
    product_subtype: Optional[str] = None,
    name: Optional[str] = None,
    weight: Optional[float] = None,
    max_age_days: int = 7
) -> Optional[GasPrice]:
    """
    Retrieve the most recent successful price data for a specific product combination
    within the specified maximum age (default 7 days).

    Args:
        country: Country name
        company: Company name
        product_type: Product type (optional)
        product_subtype: Product subtype (optional)
        name: Product name (optional)
        weight: Product weight (optional)
        max_age_days: Maximum age of fallback data in days (default 7)

    Returns:
        GasPrice object if recent data found, None otherwise
    """
    query = """
        SELECT * FROM gas_prices
        WHERE country = ? AND company = ?
        AND original_scrape_date >= datetime('now', '-{} days')
        AND is_fallback_data = FALSE
    """.format(max_age_days)

    params = [country, company]

    # Add optional filters if provided
    if product_type is not None:
        query += " AND product_type = ?"
        params.append(product_type)

    if product_subtype is not None:
        query += " AND product_subtype = ?"
        params.append(product_subtype)

    if name is not None:
        query += " AND name = ?"
        params.append(name)

    if weight is not None:
        query += " AND weight = ?"
        params.append(weight)

    query += " ORDER BY original_scrape_date DESC LIMIT 1"

    with get_db_connection() as conn:
        try:
            cursor = conn.execute(query, params)
            row = cursor.fetchone()

            if row:
                row_dict = dict(row)
                logger.info(f"Found fallback data for {country}-{company}: {row_dict['original_scrape_date']}")

                # Convert string timestamps back to datetime objects
                if row_dict.get('date_recorded'):
                    try:
                        row_dict['date_recorded'] = datetime.fromisoformat(row_dict['date_recorded'])
                    except (ValueError, TypeError):
                        row_dict['date_recorded'] = datetime.now()

                if row_dict.get('original_scrape_date'):
                    try:
                        row_dict['original_scrape_date'] = datetime.fromisoformat(row_dict['original_scrape_date'])
                    except (ValueError, TypeError):
                        row_dict['original_scrape_date'] = row_dict.get('date_recorded') or datetime.now()

                return GasPrice(**row_dict)
            else:
                logger.debug(f"No recent fallback data found for {country}-{company}")
                return None

        except sqlite3.Error as e:
            logger.error(f"Error retrieving fallback data: {str(e)}")
            raise


def create_fallback_price(
    original_price: GasPrice,
    current_scrape_date: datetime,
    country: str,
    company: str,
    url: str,
    element: Optional[str] = None,
    uid: Optional[str] = None
) -> GasPrice:
    """
    Create a fallback price record based on recent successful data.

    Args:
        original_price: The original successful price data to use as fallback
        current_scrape_date: The current scrape attempt date
        country: Country for the current scrape
        company: Company for the current scrape
        url: URL for the current scrape
        element: Element identifier for the current scrape
        uid: Unique identifier for the current scrape

    Returns:
        GasPrice object configured as fallback data
    """
    return GasPrice(
        country=country,
        company=company,
        url=url,
        element=element,
        product_type=original_price.product_type,
        product_subtype=original_price.product_subtype,
        name=original_price.name,
        weight=original_price.weight,
        price=original_price.price,
        image_path=original_price.image_path,
        date_recorded=current_scrape_date,  # Current scrape date
        uid=uid or original_price.uid,
        monthly_median_income=original_price.monthly_median_income,
        yearly_median_income=original_price.yearly_median_income,
        price_per_kg=original_price.price_per_kg,
        price_as_percent_of_monthly_income=original_price.price_as_percent_of_monthly_income,
        type=original_price.type,
        is_fallback_data=True,  # Mark as fallback data
        original_scrape_date=original_price.original_scrape_date  # Keep original scrape date
    )